# syntax=docker/dockerfile:1

# Build stage for Go application
FROM golang:1.23 AS build
WORKDIR /app

# Download Go modules
COPY container/go.mod container/go.sum ./
RUN go mod download

# Copy container src
COPY container/*.go ./

# Build Go application
RUN CGO_ENABLED=0 GOOS=linux go build -o /server

# Final stage with Ubuntu base for s3fs-fuse and Pandoc
FROM ubuntu:22.04

# Install system dependencies and tools
RUN apt-get update && apt-get install -y \
    # s3fs-fuse dependencies
    s3fs \
    fuse \
    # Pandoc
    pandoc \
    # Additional utilities
    curl \
    wget \
    ca-certificates \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

# Create directories for s3fs mount points and credentials
RUN mkdir -p /mnt/r2 /etc/s3fs

# Copy the Go server from build stage
COPY --from=build /server /usr/local/bin/server

# Create a startup script to handle s3fs mounting and server startup
RUN cat > /usr/local/bin/start.sh << 'EOF'
#!/bin/bash
set -e

# Function to mount R2 if credentials are provided
mount_r2() {
    if [ -n "$R2_ACCESS_KEY_ID" ] && [ -n "$R2_SECRET_ACCESS_KEY" ] && [ -n "$CLOUDFLARE_ACCOUNT_ID" ] && [ -n "$R2_BUCKET_NAME" ]; then
        echo "Setting up Cloudflare R2 credentials..."
        echo "$R2_ACCESS_KEY_ID:$R2_SECRET_ACCESS_KEY" > /etc/s3fs/passwd-s3fs
        chmod 600 /etc/s3fs/passwd-s3fs

        echo "Mounting Cloudflare R2 bucket..."
        s3fs "$R2_BUCKET_NAME" /mnt/r2 \
            -o url="https://${CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com" \
            -o nomixupload \
            -o endpoint=auto \
            -o passwd_file=/etc/s3fs/passwd-s3fs \
            -o allow_other \
            -o use_cache=/tmp \
            -o ensure_diskfree=100

        echo "R2 bucket mounted successfully at /mnt/r2"
    else
        echo "R2 credentials not provided, skipping R2 mount"
        echo "To enable R2 mounting, set: R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, CLOUDFLARE_ACCOUNT_ID, R2_BUCKET_NAME"
    fi
}

# Function to test Pandoc installation
test_pandoc() {
    echo "Testing Pandoc installation..."
    pandoc --version
    echo "Pandoc is ready for document conversion"
}

# Mount R2 if credentials are available
mount_r2

# Test Pandoc
test_pandoc

# Start the Go server
echo "Starting Go server..."
exec /usr/local/bin/server
EOF

# Make the startup script executable
RUN chmod +x /usr/local/bin/start.sh

# Expose the server port
EXPOSE 8080

# Set environment variables for R2 (can be overridden at runtime)
ENV R2_ACCESS_KEY_ID=""
ENV R2_SECRET_ACCESS_KEY=""
ENV CLOUDFLARE_ACCOUNT_ID=""
ENV R2_BUCKET_NAME=""

# Use the startup script as the entry point
CMD ["/usr/local/bin/start.sh"]
