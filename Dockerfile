# syntax=docker/dockerfile:1

# Build stage for TypeScript application
FROM node:20-alpine AS build
WORKDIR /app

# Copy package files
COPY container/package.json container/package-lock.json* ./
RUN npm ci --only=production

# Copy TypeScript source and config
COPY container/src ./src
COPY container/tsconfig.json ./

# Install dev dependencies and build
RUN npm install --save-dev typescript tsx @types/node
RUN npm run build

# Final stage with Ubuntu base for s3fs-fuse and Pandoc
FROM ubuntu:22.04

# Install system dependencies and tools
RUN apt-get update && apt-get install -y \
    # Node.js
    nodejs \
    npm \
    # s3fs-fuse dependencies
    s3fs \
    fuse \
    # Pandoc
    pandoc \
    # Additional utilities
    curl \
    wget \
    ca-certificates \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

# Create directories for s3fs mount points and credentials
RUN mkdir -p /mnt/r2 /etc/s3fs

# Copy the built TypeScript application from build stage
COPY --from=build /app/dist /usr/local/app/dist
COPY --from=build /app/node_modules /usr/local/app/node_modules
COPY --from=build /app/package.json /usr/local/app/package.json

# Create a startup script to handle s3fs mounting and server startup
RUN cat > /usr/local/bin/start.sh << 'EOF'
#!/bin/bash
set -e

# Function to mount R2 if credentials are provided
mount_r2() {
    if [ -n "$R2_ACCESS_KEY_ID" ] && [ -n "$R2_SECRET_ACCESS_KEY" ] && [ -n "$CLOUDFLARE_ACCOUNT_ID" ] && [ -n "$R2_BUCKET_NAME" ]; then
        echo "Setting up Cloudflare R2 credentials..."
        echo "$R2_ACCESS_KEY_ID:$R2_SECRET_ACCESS_KEY" > /etc/s3fs/passwd-s3fs
        chmod 600 /etc/s3fs/passwd-s3fs

        echo "Mounting Cloudflare R2 bucket..."
        s3fs "$R2_BUCKET_NAME" /mnt/r2 \
            -o url="https://${CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com" \
            -o nomixupload \
            -o endpoint=auto \
            -o passwd_file=/etc/s3fs/passwd-s3fs \
            -o allow_other \
            -o use_cache=/tmp \
            -o ensure_diskfree=100

        echo "R2 bucket mounted successfully at /mnt/r2"
    else
        echo "R2 credentials not provided, skipping R2 mount"
        echo "To enable R2 mounting, set: R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, CLOUDFLARE_ACCOUNT_ID, R2_BUCKET_NAME"
    fi
}

# Function to test Pandoc installation
test_pandoc() {
    echo "Testing Pandoc installation..."
    pandoc --version
    echo "Pandoc is ready for document conversion"
}

# Mount R2 if credentials are available
mount_r2

# Test Pandoc
test_pandoc

# Start the Node.js server
echo "Starting TypeScript server..."
cd /usr/local/app
exec node dist/index.js
EOF

# Make the startup script executable
RUN chmod +x /usr/local/bin/start.sh

# Set working directory
WORKDIR /usr/local/app

# Expose the server port
EXPOSE 8002

# Set environment variables for R2 (can be overridden at runtime)
ENV R2_ACCESS_KEY_ID=""
ENV R2_SECRET_ACCESS_KEY=""
ENV CLOUDFLARE_ACCOUNT_ID=""
ENV R2_BUCKET_NAME=""
ENV PORT="8002"
ENV R2_ENDPOINT=""

# Use the startup script as the entry point
CMD ["/usr/local/bin/start.sh"]
