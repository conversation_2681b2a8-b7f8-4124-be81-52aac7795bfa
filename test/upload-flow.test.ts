/**
 * @vitest-environment node
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { CreateRequest } from '../src/requests/create';
import { CompleteRequest } from '../src/requests/complete';
import { AbortRequest } from '../src/requests/abort';
import { StatusRequest } from '../src/requests/status';
import { UploadRecord } from '../src/lib/UploadRecord';

// Mock implementations for testing
class MockR2Bucket {
  async createMultipartUpload(key: string) {
    return { key, uploadId: 'test-upload-123' };
  }

  resumeMultipartUpload(key: string, uploadId: string) {
    return {
      complete: async (parts: any[]) => ({
        key,
        httpEtag: '"test-etag"'
      }),
      abort: async () => {}
    };
  }
}

class MockWorkflow {
  async create(params: any) {
    return { id: 'workflow-instance-456' };
  }
}

class MockD1Database {
  private data: Map<string, any> = new Map();

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        run: async () => ({ success: true }),
        first: async () => {
          if (query.includes("SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = ?")) {
            const uploadId = params[0];
            return this.data.get(`upload_${uploadId}`) || null;
          }
          return null;
        },
        all: async () => ({
          results: Array.from(this.data.values()).filter(v => v.properties)
        })
      }),
      run: async () => ({ success: true }),
      first: async () => null,
      all: async () => ({ results: [] })
    };
  }

  setTestData(key: string, value: any) {
    this.data.set(key, value);
  }

  clear() {
    this.data.clear();
  }
}

describe('Upload Flow Integration', () => {
  let mockBucket: MockR2Bucket;
  let mockWorkflow: MockWorkflow;
  let mockDb: MockD1Database;

  beforeEach(() => {
    mockBucket = new MockR2Bucket();
    mockWorkflow = new MockWorkflow();
    mockDb = new MockD1Database();
    mockDb.clear();
  });

  it('should handle complete upload flow', async () => {
    // 1. Create upload
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any, 'test-file.txt');
    const createResponse = await createRequest.execute();

    expect(createResponse.status).toBe(200);
    const createData = await createResponse.json();
    expect(createData).toEqual({
      key: 'test-file.txt',
      uploadId: 'test-upload-123'
    });

    // 2. Check initial status (should be pending)
    mockDb.setTestData('upload_test-upload-123', {
      id: 'test-id',
      properties: '{"upload_id":"test-upload-123","workflow_instance_id":"","key":"test-file.txt","status":"pending"}',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    });

    const statusRequest = new StatusRequest(mockDb as any, 'test-file.txt', 'test-upload-123');
    const statusResponse = await statusRequest.execute();

    expect(statusResponse.status).toBe(200);
    const statusData = await statusResponse.json() as any;
    expect(statusData.status).toBe('pending');
    expect(statusData.workflowInstanceId).toBe('');

    // 3. Complete upload
    const completeRequest = new CompleteRequest(
      mockBucket as any,
      mockWorkflow as any,
      mockDb as any,
      'test-file.txt',
      'test-upload-123',
      { parts: [{ partNumber: 1, etag: '"test-etag"' }] }
    );

    const completeResponse = await completeRequest.execute();
    expect(completeResponse.status).toBe(200);
  });

  it('should handle upload abort', async () => {
    // Create upload first
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any, 'test-file.txt');
    await createRequest.execute();

    // Abort upload
    const abortRequest = new AbortRequest(mockBucket as any, mockDb as any, 'test-file.txt', 'test-upload-123');
    const abortResponse = await abortRequest.execute();

    expect(abortResponse.status).toBe(204);
  });

  it('should return 404 for non-existent upload status', async () => {
    const statusRequest = new StatusRequest(mockDb as any, 'test-file.txt', 'non-existent-upload');
    const statusResponse = await statusRequest.execute();

    expect(statusResponse.status).toBe(404);
  });
});
