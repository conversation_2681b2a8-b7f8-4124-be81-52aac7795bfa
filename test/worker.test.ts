import { describe, it, expect } from 'vitest';
import app from '../src/index';

// Mock D1Database for testing
const mockDB = {
  prepare: (query: string) => ({
    bind: (...params: any[]) => ({
      run: async () => ({ success: true }),
      first: async () => null,
      all: async () => ({ results: [] })
    }),
    run: async () => ({ success: true }),
    first: async () => null,
    all: async () => ({ results: [] })
  })
};

// Mock environment for testing
const mockEnv = {
  DB: mockDB,
  COMPRESSOR_WORKFLOW: {
    create: async (params: any) => ({
      id: 'mock-workflow-id-123'
    })
  },
  BUCKET: {
    createMultipartUpload: async (key: string) => ({
      key,
      uploadId: 'test-upload-id-123'
    }),
    resumeMultipartUpload: (key: string, uploadId: string) => ({
      uploadPart: async (partNumber: number, body: any) => ({
        partNumber,
        etag: `"etag-${partNumber}"`
      }),
      complete: async (parts: any[]) => ({
        key,
        httpEtag: '"completed-etag"'
      }),
      abort: async () => {}
    })
  }
};

// Mock environment with base path for testing
const mockEnvWithBasePath = {
  ...mockEnv,
  BASE_PATH: '/api/upload'
};

describe('Multipart Upload Worker', () => {
  it('should create multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt', {
      method: 'POST'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    const data = await res.json() as any;
    expect(data.key).toBe('test-file.txt');
    expect(data.uploadId).toBe('test-upload-id-123');
  });

  it('should upload a part', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/1', {
      method: 'PUT',
      body: 'test data'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    const data = await res.json() as any;
    expect(data.partNumber).toBe(1);
    expect(data.etag).toBe('"etag-1"');
  });

  it('should complete multipart upload', async () => {
    const parts = [
      { partNumber: 1, etag: '"etag-1"' },
      { partNumber: 2, etag: '"etag-2"' }
    ];

    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ parts })
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(200);
    expect(res.headers.get('etag')).toBe('"completed-etag"');
  });

  it('should abort multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'DELETE'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(204);
  });

  it('should return 400 for invalid part number', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/invalid', {
      method: 'PUT',
      body: 'test data'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Invalid part number');
  });

  it('should return 400 for missing request body in upload part', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id/1', {
      method: 'PUT'
      // No body
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Missing request body');
  });

  it('should return 400 for missing body in complete request', async () => {
    const req = new Request('http://localhost/test-file.txt/test-upload-id', {
      method: 'POST'
      // No body
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(400);
    const text = await res.text();
    expect(text).toBe('Missing or incomplete body');
  });

  it('should return 404 for unknown endpoint', async () => {
    const req = new Request('http://localhost/unknown-endpoint', {
      method: 'GET'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(404);
    const text = await res.text();
    expect(text).toBe('Not Found');
  });

  it('should return 404 for invalid path structure', async () => {
    const req = new Request('http://localhost/test-file.txt/too/many/path/segments', {
      method: 'POST'
    }) as any;

    const res = await app.fetch(req, mockEnv as any);

    expect(res.status).toBe(404);
    const text = await res.text();
    expect(text).toBe('Not Found');
  });

  describe('Base Path Support', () => {
    it('should handle requests with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt', {
        method: 'POST'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.key).toBe('test-file.txt');
      expect(data.uploadId).toBe('test-upload-id-123');
    });

    it('should return 404 for requests without base path when BASE_PATH is configured', async () => {
      const req = new Request('http://localhost/test-file.txt', {
        method: 'POST'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(404);
      const text = await res.text();
      expect(text).toBe('Not Found');
    });

    it('should handle upload part with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id/1', {
        method: 'PUT',
        body: 'test data'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      const data = await res.json() as any;
      expect(data.partNumber).toBe(1);
      expect(data.etag).toBe('"etag-1"');
    });

    it('should handle complete upload with base path', async () => {
      const parts = [
        { partNumber: 1, etag: '"etag-1"' },
        { partNumber: 2, etag: '"etag-2"' }
      ];

      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ parts })
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(200);
      expect(res.headers.get('etag')).toBe('"completed-etag"');
    });

    it('should handle abort upload with base path', async () => {
      const req = new Request('http://localhost/api/upload/test-file.txt/test-upload-id', {
        method: 'DELETE'
      }) as any;

      const res = await app.fetch(req, mockEnvWithBasePath as any);

      expect(res.status).toBe(204);
    });
  });
});
