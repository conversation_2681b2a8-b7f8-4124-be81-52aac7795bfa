import { Hono } from 'hono';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { Pandoc } from 'pandoc-ts';
import { createServer } from 'http';

const app = new Hono();

// Types for conversion options
interface ConversionOptions {
  from?: string;
  to?: string;
  options?: string[];
}

// Initialize S3 client for R2
const s3Client = new S3Client({
  region: 'auto',
  endpoint: process.env.R2_ENDPOINT || 'https://your-account-id.r2.cloudflarestorage.com',
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
  },
});

// Helper function to get content type based on format
function getContentType(format: string): string {
  const contentTypes: Record<string, string> = {
    'html': 'text/html',
    'html5': 'text/html',
    'markdown': 'text/markdown',
    'md': 'text/markdown',
    'latex': 'application/x-latex',
    'tex': 'application/x-latex',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'odt': 'application/vnd.oasis.opendocument.text',
    'rtf': 'application/rtf',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'epub': 'application/epub+zip'
  };

  return contentTypes[format.toLowerCase()] || 'text/plain';
}

// Helper function to stream R2 object to string
async function streamToString(stream: ReadableStream): Promise<string> {
  const reader = stream.getReader();
  const chunks: Uint8Array[] = [];

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }
  } finally {
    reader.releaseLock();
  }

  const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;

  for (const chunk of chunks) {
    result.set(chunk, offset);
    offset += chunk.length;
  }

  return new TextDecoder().decode(result);
}

// Main conversion endpoint
app.post('/convert', async (c) => {
  try {
    // Get R2 key from query parameters
    const r2Key = c.req.query('key');
    if (!r2Key) {
      return c.json({ error: 'R2 key is required in query parameters' }, 400);
    }

    // Get conversion options from request body
    const conversionOptions: ConversionOptions = await c.req.json().catch(() => ({}));

    // Fetch document from R2
    const getObjectCommand = new GetObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME || 'content',
      Key: r2Key,
    });

    const response = await s3Client.send(getObjectCommand);

    if (!response.Body) {
      return c.json({ error: 'Document not found in R2' }, 404);
    }

    // Convert the stream to string
    const documentContent = await streamToString(response.Body as ReadableStream);

    // Prepare pandoc options
    const pandocOptions: any = {
      from: conversionOptions.from || 'markdown',
      to: conversionOptions.to || 'html',
    };

    // Add additional options if provided
    if (conversionOptions.options && conversionOptions.options.length > 0) {
      pandocOptions.args = conversionOptions.options;
    }

    // Convert the document using pandoc-ts
    const pandocInstance = new Pandoc(documentContent, pandocOptions.to);
    const convertedContent = await new Promise<string>((resolve, reject) => {
      pandocInstance.convert(documentContent, (err, result) => {
        if (err) reject(err);
        else resolve(typeof result === 'string' ? result : '');
      });
    });

    // Determine the content type based on the output format
    const contentType = getContentType(conversionOptions.to || 'html');

    // Return the converted content
    c.header('Content-Type', contentType);
    c.header('Cache-Control', 'public, max-age=3600');

    return c.text(convertedContent);

  } catch (error) {
    console.error('Conversion error:', error);
    return c.json({
      error: 'Document conversion failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Health check endpoint
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start the server
const port = parseInt(process.env.PORT || '8002');
console.log(`Server is running on port ${port}`);

const server = createServer(async (req, res) => {
  try {
    // Convert Node.js request to Fetch API request
    const url = `http://localhost:${port}${req.url}`;

    let body: string | undefined;
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      const chunks: Buffer[] = [];
      for await (const chunk of req) {
        chunks.push(chunk);
      }
      body = Buffer.concat(chunks).toString();
    }

    const request = new Request(url, {
      method: req.method,
      headers: req.headers as HeadersInit,
      body: body,
    });

    const response = await app.fetch(request);

    res.statusCode = response.status;
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });

    if (response.body) {
      const reader = response.body.getReader();
      const pump = async (): Promise<void> => {
        const { done, value } = await reader.read();
        if (done) {
          res.end();
          return;
        }
        res.write(value);
        return pump();
      };
      await pump();
    } else {
      res.end();
    }
  } catch (error) {
    console.error('Server error:', error);
    res.statusCode = 500;
    res.end('Internal Server Error');
  }
});

server.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});
