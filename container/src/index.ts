import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { Pandoc } from 'pandoc-ts';
import { join } from 'path';

const app = new Hono();

// Types for conversion options
interface ConversionOptions {
  from?: string;
  to?: string;
  options?: string[];
}

// R2 mount path
const R2_MOUNT_PATH = '/mnt/r2';

// Helper function to get content type based on format
function getContentType(format: string): string {
  const contentTypes: Record<string, string> = {
    'html': 'text/html',
    'html5': 'text/html',
    'markdown': 'text/markdown',
    'md': 'text/markdown',
    'latex': 'application/x-latex',
    'tex': 'application/x-latex',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'odt': 'application/vnd.oasis.opendocument.text',
    'rtf': 'application/rtf',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'epub': 'application/epub+zip'
  };

  return contentTypes[format.toLowerCase()] || 'text/plain';
}

app.post('/convert/:key{.+}', async (c) => {
	const key = c.req.param('key');
  try {
	const options: ConversionOptions = await c.req.json<ConversionOptions>().catch(() => ({}));

    // Get file path from mounted R2 filesystem
    const filePath = join(R2_MOUNT_PATH, key);

    // Prepare pandoc output format
    const outputFormat = {
      name: 'output',
      format: options.to || 'html',
      outBin: false,
      enc: 'utf8' as BufferEncoding
    };

    // Convert the document using pandoc-ts
    const inputFormat = options.from || 'markdown';
    const pandocInstance = new Pandoc(inputFormat, outputFormat);

    const convertedContent = await new Promise<string>((resolve, reject) => {
      pandocInstance.convertFile(filePath, (result, err) => {
        if (err) reject(err);
        else if (result && result.output) {
          resolve(typeof result.output === 'string' ? result.output : '');
        } else {
          resolve('');
        }
      });
    });

    // Determine the content type based on the output format
    const contentType = getContentType(options.to || 'html');

    // Return the converted content
    c.header('Content-Type', contentType);
    c.header('Cache-Control', 'public, max-age=3600');

    return c.text(convertedContent);

  } catch (error) {
    console.error('Conversion error:', error);
    return c.json({
      error: 'Document conversion failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Health check endpoint
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start the server
const port = parseInt(process.env.PORT || '8002');

const server = serve({
  fetch: app.fetch,
  port,
});

console.log(`Server is running on port ${port}`);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  server.close((err) => {
    if (err) {
      console.error('Error during shutdown:', err);
      process.exit(1);
    }
    process.exit(0);
  });
});
