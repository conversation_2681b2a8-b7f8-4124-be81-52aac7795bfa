import { Hono } from 'hono';
import { Pandoc } from 'pandoc-ts';
import { createServer } from 'http';
import { join } from 'path';

const app = new Hono();

// Types for conversion options
interface ConversionOptions {
  from?: string;
  to?: string;
  options?: string[];
}

// R2 mount path
const R2_MOUNT_PATH = '/mnt/r2';

// Helper function to get content type based on format
function getContentType(format: string): string {
  const contentTypes: Record<string, string> = {
    'html': 'text/html',
    'html5': 'text/html',
    'markdown': 'text/markdown',
    'md': 'text/markdown',
    'latex': 'application/x-latex',
    'tex': 'application/x-latex',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'odt': 'application/vnd.oasis.opendocument.text',
    'rtf': 'application/rtf',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'epub': 'application/epub+zip'
  };

  return contentTypes[format.toLowerCase()] || 'text/plain';
}

// Main conversion endpoint
app.post('/convert', async (c) => {
  try {
    // Get R2 key from query parameters
    const r2Key = c.req.query('key');
    if (!r2Key) {
      return c.json({ error: 'R2 key is required in query parameters' }, 400);
    }

    // Get conversion options from request body
    const conversionOptions: ConversionOptions = await c.req.json().catch(() => ({}));

    // Get file path from mounted R2 filesystem
    const filePath = join(R2_MOUNT_PATH, r2Key);

    // Prepare pandoc output format
    const outputFormat = {
      name: 'output',
      format: conversionOptions.to || 'html',
      outBin: false,
      enc: 'utf8' as BufferEncoding
    };

    // Convert the document using pandoc-ts
    const inputFormat = conversionOptions.from || 'markdown';
    const pandocInstance = new Pandoc(inputFormat, outputFormat);

    const convertedContent = await new Promise<string>((resolve, reject) => {
      pandocInstance.convertFile(filePath, (result, err) => {
        if (err) reject(err);
        else if (result && result.output) {
          resolve(typeof result.output === 'string' ? result.output : '');
        } else {
          resolve('');
        }
      });
    });

    // Determine the content type based on the output format
    const contentType = getContentType(conversionOptions.to || 'html');

    // Return the converted content
    c.header('Content-Type', contentType);
    c.header('Cache-Control', 'public, max-age=3600');

    return c.text(convertedContent);

  } catch (error) {
    console.error('Conversion error:', error);
    return c.json({
      error: 'Document conversion failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Health check endpoint
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start the server
const port = parseInt(process.env.PORT || '8002');
console.log(`Server is running on port ${port}`);

const server = createServer(async (req, res) => {
  try {
    // Convert Node.js request to Fetch API request
    const url = `http://localhost:${port}${req.url}`;

    let body: string | undefined;
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      const chunks: Buffer[] = [];
      for await (const chunk of req) {
        chunks.push(chunk);
      }
      body = Buffer.concat(chunks).toString();
    }

    const request = new Request(url, {
      method: req.method,
      headers: req.headers as HeadersInit,
      body: body,
    });

    const response = await app.fetch(request);

    res.statusCode = response.status;
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });

    if (response.body) {
      const reader = response.body.getReader();
      const pump = async (): Promise<void> => {
        const { done, value } = await reader.read();
        if (done) {
          res.end();
          return;
        }
        res.write(value);
        return pump();
      };
      await pump();
    } else {
      res.end();
    }
  } catch (error) {
    console.error('Server error:', error);
    res.statusCode = 500;
    res.end('Internal Server Error');
  }
});

server.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});
