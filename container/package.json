{"name": "api-upload-container", "version": "1.0.0", "private": true, "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@hono/node-server": "^1.18.1", "hono": "^4.9.0", "pandoc-ts": "^1.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}}