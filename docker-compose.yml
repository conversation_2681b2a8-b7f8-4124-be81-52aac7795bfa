version: '3.8'

services:
  api-upload:
    build: .
    ports:
      - "8080:8080"
    environment:
      # Cloudflare R2 Configuration
      # Replace these with your actual R2 credentials
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID:-}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY:-}
      - CLOUDFLARE_ACCOUNT_ID=${CLOUDFLARE_ACCOUNT_ID:-}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME:-}

    # Required for FUSE mounting (s3fs)
    privileged: true

    # Optional: Mount host directories for persistent storage and caching
    volumes:
      - ./temp:/tmp/conversions  # For conversion temporary files
      - ./logs:/var/log          # For application logs

    # Health check to ensure the service is running
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Restart policy
    restart: unless-stopped

    # Resource limits (adjust as needed)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Optional: Add a reverse proxy for production use
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
    depends_on:
      - api-upload
    restart: unless-stopped
    profiles:
      - production  # Only start with --profile production

# Create named volumes for persistent data
volumes:
  temp_data:
    driver: local
  log_data:
    driver: local

# Network configuration
networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
