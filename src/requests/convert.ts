import { Hono } from 'hono';
import { pandoc } from 'pandoc-ts';

export interface ConversionOptions {
  from?: string;
  to?: string;
  options?: string[];
}

export class ConvertRequest {
  private bucket: R2Bucket;
  private r2Key: string;
  private conversionOptions: ConversionOptions;

  constructor(bucket: R2Bucket, r2Key: string, conversionOptions: ConversionOptions) {
    this.bucket = bucket;
    this.r2Key = r2Key;
    this.conversionOptions = conversionOptions;
  }

  async execute(): Promise<Response> {
    try {
      // Fetch the document from R2
      const object = await this.bucket.get(this.r2Key);
      
      if (!object) {
        return new Response(JSON.stringify({ error: 'Document not found in R2' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Get the document content
      const documentContent = await object.text();
      
      // Prepare pandoc options
      const pandocOptions: any = {
        from: this.conversionOptions.from || 'markdown',
        to: this.conversionOptions.to || 'html',
      };

      // Add additional options if provided
      if (this.conversionOptions.options && this.conversionOptions.options.length > 0) {
        pandocOptions.args = this.conversionOptions.options;
      }

      // Convert the document using pandoc-ts
      const convertedContent = await pandoc(documentContent, pandocOptions);

      // Determine the content type based on the output format
      const contentType = this.getContentType(this.conversionOptions.to || 'html');

      return new Response(convertedContent, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=3600'
        }
      });

    } catch (error) {
      console.error('Conversion error:', error);
      return new Response(JSON.stringify({ 
        error: 'Document conversion failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  private getContentType(format: string): string {
    const contentTypes: Record<string, string> = {
      'html': 'text/html',
      'html5': 'text/html',
      'markdown': 'text/markdown',
      'md': 'text/markdown',
      'latex': 'application/x-latex',
      'tex': 'application/x-latex',
      'pdf': 'application/pdf',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'odt': 'application/vnd.oasis.opendocument.text',
      'rtf': 'application/rtf',
      'txt': 'text/plain',
      'json': 'application/json',
      'xml': 'application/xml',
      'epub': 'application/epub+zip'
    };

    return contentTypes[format.toLowerCase()] || 'text/plain';
  }
}
