import { UploadRequest } from './upload';
import { UploadRecord } from '../lib/UploadRecord';

export class AbortRequest extends UploadRequest {
  private readonly dbService: UploadRecord;

  constructor(bucket: R2Bucket, db: D1Database, key: string, uploadId: string) {
    super(bucket, key, uploadId);
    this.dbService = new UploadRecord(db);
  }

  async execute(): Promise<Response> {
    try {
      await this.upload.abort();

      // Clean up database record when upload is aborted
      await this.dbService.delete(this.uploadId);
    } catch (error: any) {
      return this.error(error);
    }
    return new Response(null, { status: 204 });
  }
}
