import { UploadRequest } from './upload';
import { CompleteBody } from '../types';
import { UploadRecord } from '../lib/UploadRecord';

export class CompleteRequest extends UploadRequest {
	private readonly dbService: UploadRecord;

	constructor(bucket: R2Bucket, private readonly workflow: Workflow, db: D1Database, key: string, uploadId: string, private readonly body: CompleteBody | null) {
		super(bucket, key, uploadId);
		this.dbService = new UploadRecord(db);
	}

	async execute(): Promise<Response> {
		if (this.body === null) {
			return this.error("Missing or incomplete body");
		}
		try {
			const object = await this.upload.complete(this.body.parts);

			// Create workflow instance
			const workflowInstance = await this.workflow.create({ params: { r2Path: object.key, id: object.key } });

			// Update existing upload record with workflow instance ID
			await this.dbService.updateWorkflowInstanceId(this.uploadId, workflowInstance.id);

			// Update status to processing since workflow has started
			await this.dbService.update(this.uploadId, 'processing');

			return new Response(null, {
				headers: {
					etag: object.httpEtag
				}
			});
		} catch (error: any) {
			return this.error(error);
		}
	}
}
