import { DurableObject } from 'cloudflare:workers';
import { Env } from '../types';

export class Compressor extends DurableObject<Env> {
	container: Container;
	monitor?: Promise<unknown>;

	constructor(ctx: DurableObjectState, env: Env) {
		super(ctx, env);
		if (ctx.container === undefined) {
			throw new Error('no container');
		}
		this.container = ctx.container;
		ctx.blockConcurrencyWhile(async () => {
			if (!this.container.running) {
				await this.container.start({
					env: {
						CLOUDFLARE_ACCOUNT_ID: env.CLOUDFLARE_ACCOUNT_ID,
						R2_ACCESS_KEY_ID: env.R2_ACCESS_KEY_ID,
						R2_SECRET_ACCESS_KEY: env.R2_SECRET_ACCESS_KEY,
						R2_BUCKET_NAME: env.R2_BUCKET_NAME
					},
					entrypoint: ['/server'],
					enableInternet: false
				});
			}
			this.monitor = this.container.monitor().then(() => {
				console.log('Container exited?');
			});
		});
	}

	async init() {
		console.log('Starting container');
	}

	async logs() {
		return await this.container.getTcpPort(8002).fetch('http://container');
	}

	async destroy() {
		await this.ctx.container?.destroy();
		await this.ctx.storage.deleteAll();
		await this.ctx.storage.deleteAlarm();
		await this.ctx.storage.sync();
		this.ctx.abort();
	}

	async fetch(req: Request): Promise<Response> {
		void this.container.getTcpPort(8002).fetch('http://container/compressions', {
			method: 'PUT',
			body: req.body
		});
		return await this.container.getTcpPort(8002).fetch('http://container/compressions');
	}
}
