import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from 'cloudflare:workers';
import { Env } from '../types';
import { UploadRecord } from './UploadRecord';

type Params = {
	r2Path: string;
};

function sleep(ms: number) {
	return new Promise((res) => setTimeout(res, ms));
}

export class CompressorWorkflow extends WorkflowEntrypoint<Env, Params> {
	async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
		const container = this.env.COMPRESSOR.get(this.env.COMPRESSOR.idFromName(event.instanceId));
		const dbService = new UploadRecord(this.env.DB);

		await step.do('wait for container to be healthy and pass r2 object, and put on another object', async () => {
			const tries = 10;
			await container.init();

			const waitUntilContainerIsOk = async () => {
				let lastErr: unknown;
				for (let i = 0; i < tries; i++) {
					try {
						await container.logs();
						return;
					} catch (err) {
						console.error('transient error:', err instanceof Error ? err.message : JSON.stringify(err));
						await sleep(500);
						lastErr = err;
					}
				}

				throw lastErr;
			};

			await waitUntilContainerIsOk();

			const object = await this.env.BUCKET.get(event.payload.r2Path);
			if (object === null) {
				console.error('Object not found: ' + event.payload.r2Path);
				return;
			}

			try {
				const result = await container.fetch(new Request('http://compressor', { method: 'POST', body: object.body }));
				await this.env.BUCKET.put(`results${event.payload.r2Path}`, result.body);

				// Update status to completed on successful compression
				const upload = await dbService.getByWorkflowInstanceId(event.instanceId);
				if (upload) {
					await dbService.update(upload.properties.upload_id, 'completed');
				}
			} catch (err) {
				console.error('There was an error compressing the object', err instanceof Error ? err.message : JSON.stringify(err));

				// Update status to failed on error
				const upload = await dbService.getByWorkflowInstanceId(event.instanceId);
				if (upload) {
					await dbService.update(upload.properties.upload_id, 'failed');
				}

				throw err;
			}
		});

		await step.do('destroy', async () => {
			await container.destroy();
		});
	}
}
